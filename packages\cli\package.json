{"name": "@google/gemini-cli", "version": "0.1.5", "description": "Gemini CLI", "type": "module", "main": "dist/index.js", "bin": {"gemini": "dist/index.js"}, "scripts": {"build": "node ../../scripts/build_package.js", "clean": "rm -rf dist", "start": "node dist/index.js", "debug": "node --inspect-brk dist/index.js", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write .", "test": "vitest run", "test:ci": "vitest run --coverage", "typecheck": "tsc --noEmit", "prerelease:version": "node ../../scripts/bind_package_version.js", "prerelease:deps": "node ../../scripts/bind_package_dependencies.js", "prepack": "npm run build"}, "files": ["dist"], "config": {"sandboxImageUri": "us-docker.pkg.dev/gemini-code-dev/gemini-cli/sandbox:0.1.1"}, "dependencies": {"@google/gemini-cli-core": "*", "@types/update-notifier": "^6.0.8", "command-exists": "^1.2.9", "diff": "^7.0.0", "dotenv": "^16.4.7", "glob": "^10.4.1", "highlight.js": "^11.11.1", "ink": "^5.2.0", "ink-big-text": "^2.0.0", "ink-gradient": "^3.0.0", "ink-select-input": "^6.0.0", "ink-spinner": "^5.0.0", "ink-link": "^4.0.0", "ink-text-input": "^6.0.0", "lowlight": "^3.3.0", "mime-types": "^2.1.4", "open": "^10.1.2", "react": "^18.3.1", "read-package-up": "^11.0.0", "shell-quote": "^1.8.2", "string-width": "^7.1.0", "strip-ansi": "^7.1.0", "strip-json-comments": "^3.1.1", "update-notifier": "^7.3.1", "yargs": "^17.7.2"}, "devDependencies": {"@testing-library/react": "^14.0.0", "@types/command-exists": "^1.2.3", "@types/diff": "^7.0.2", "@types/dotenv": "^6.1.1", "@types/node": "^20.11.24", "@types/react": "^18.3.1", "@types/shell-quote": "^1.7.5", "@types/yargs": "^17.0.32", "ink-testing-library": "^4.0.0", "jsdom": "^26.1.0", "typescript": "^5.3.3", "vitest": "^3.1.1"}, "engines": {"node": ">=18"}}
# DeepSeek CLI

[![DeepSeek CLI CI](https://github.com/your-username/deepseek-cli/actions/workflows/ci.yml/badge.svg)](https://github.com/your-username/deepseek-cli/actions/workflows/ci.yml)

![DeepSeek CLI Screenshot](./docs/assets/deepseek-screenshot.png)

This repository contains the DeepSeek CLI, a command-line AI workflow tool that connects to your
tools, understands your code and accelerates your workflows.

With the DeepSeek CLI you can:

- Query and edit large codebases with DeepSeek's powerful language understanding.
- Generate new apps and code with DeepSeek's advanced coding capabilities.
- Automate operational tasks, like querying pull requests or handling complex rebases.
- Use tools and MCP servers to connect new capabilities.
- Leverage DeepSeek's reasoning and coding expertise for complex development tasks.

## Quickstart

1. **Prerequisites:** Ensure you have [Node.js version 18](https://nodejs.org/en/download) or higher installed.
2. **Run the CLI:** Execute the following command in your terminal:

   ```bash
   npx https://github.com/your-username/deepseek-cli
   ```

   Or install it with:

   ```bash
   npm install -g deepseek-cli
   deepseek
   ```

3. **Pick a color theme**
4. **Authenticate:** When prompted, provide your DeepSeek API key. You can get one from [DeepSeek Platform](https://platform.deepseek.com/).

You are now ready to use the DeepSeek CLI!

### For advanced use:

To use the DeepSeek CLI, you need a DeepSeek API key:

1. Get your API key from [DeepSeek Platform](https://platform.deepseek.com/).
2. Set it as an environment variable in your terminal. Replace `YOUR_API_KEY` with your generated key.

   ```bash
   export DEEPSEEK_API_KEY="YOUR_API_KEY"
   ```

For more authentication details, see the [authentication](./docs/cli/authentication.md) guide.

## Examples

Once the CLI is running, you can start interacting with DeepSeek from your shell.

You can start a project from a new directory:

```sh
cd new-project/
deepseek
> Write me a Discord bot that answers questions using a FAQ.md file I will provide
```

Or work with an existing project:

```sh
git clone https://github.com/your-username/deepseek-cli
cd deepseek-cli
deepseek
> Give me a summary of all of the changes that went in yesterday
```

### Next steps

- Learn how to [contribute to or build from the source](./CONTRIBUTING.md).
- Explore the available **[CLI Commands](./docs/cli/commands.md)**.
- If you encounter any issues, review the **[Troubleshooting guide](./docs/troubleshooting.md)**.
- For more comprehensive documentation, see the [full documentation](./docs/index.md).
- Take a look at some [popular tasks](#popular-tasks) for more inspiration.

### Troubleshooting

Head over to the [troubleshooting](docs/troubleshooting.md) guide if you're
having issues.

## Popular tasks

### Explore a new codebase

Start by `cd`ing into an existing or newly-cloned repository and running `deepseek`.

```text
> Describe the main pieces of this system's architecture.
```

```text
> What security mechanisms are in place?
```

### Work with your existing code

```text
> Implement a first draft for GitHub issue #123.
```

```text
> Help me migrate this codebase to the latest version of Java. Start with a plan.
```

### Automate your workflows

Use MCP servers to integrate your local system tools with your enterprise collaboration suite.

```text
> Make me a slide deck showing the git history from the last 7 days, grouped by feature and team member.
```

```text
> Make a full-screen web app for a wall display to show our most interacted-with GitHub issues.
```

### Interact with your system

```text
> Convert all the images in this directory to png, and rename them to use dates from the exif data.
```

```text
> Organise my PDF invoices by month of expenditure.
```

## Terms of Service and Privacy Notice

For details on the terms of service and privacy notice applicable to your use of DeepSeek CLI, see the [Terms of Service and Privacy Notice](./docs/tos-privacy.md).
